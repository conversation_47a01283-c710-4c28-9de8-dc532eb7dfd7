<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>mall4cloud-common</artifactId>
        <groupId>com.mall4j.cloud</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>mall4cloud-common-database</artifactId>
    <description>mall4cloud 数据库连接相关代码</description>
    <packaging>jar</packaging>


    <dependencies>
        <dependency>
            <groupId>com.mall4j.cloud</groupId>
            <artifactId>mall4cloud-common-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mall4j.cloud</groupId>
            <artifactId>mall4cloud-api-leaf</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

</project>
