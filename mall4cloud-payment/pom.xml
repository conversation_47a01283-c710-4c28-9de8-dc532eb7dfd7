<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>mall4cloud</artifactId>
        <groupId>com.mall4j.cloud</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>mall4cloud-payment</artifactId>
    <description>mall4cloud 支付服务</description>
    <packaging>jar</packaging>

    <dependencies>
        <!--注册中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mall4j.cloud</groupId>
            <artifactId>mall4cloud-common-database</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mall4j.cloud</groupId>
            <artifactId>mall4cloud-common-rocketmq</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mall4j.cloud</groupId>
            <artifactId>mall4cloud-common-security</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mall4j.cloud</groupId>
            <artifactId>mall4cloud-api-leaf</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mall4j.cloud</groupId>
            <artifactId>mall4cloud-api-order</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.mall4j.cloud</groupId>
            <artifactId>mall4cloud-api-platform</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
            </plugin>
        </plugins>
    </build>
</project>
