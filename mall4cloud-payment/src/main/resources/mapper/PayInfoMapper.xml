<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mall4j.cloud.payment.mapper.PayInfoMapper">
  <resultMap id="payInfoMap" type="com.mall4j.cloud.payment.model.PayInfo">
    <id column="pay_id" property="payId" />
    <result column="create_time" property="createTime"/>
    <result column="update_time" property="updateTime"/>
    <result column="user_id" property="userId"/>
    <result column="biz_pay_no" property="bizPayNo"/>
    <result column="sys_type" property="sysType"/>
    <result column="pay_status" property="payStatus"/>
    <result column="pay_amount" property="payAmount"/>
    <result column="version" property="version"/>
    <result column="callback_content" property="callbackContent"/>
    <result column="callback_time" property="callbackTime"/>
    <result column="confirm_time" property="confirmTime"/>
  </resultMap>
  <sql id="Vo_Column_List">
    `pay_id`,`create_time`,`update_time`,`user_id`,`order_ids`,`biz_pay_no`,`sys_type`,`pay_status`,
    `pay_amount`,`version`,`callback_content`,`callback_time`,`confirm_time`
  </sql>
  <select id="getByPayId" resultMap="payInfoMap">
    select <include refid="Vo_Column_List"/> from pay_info where pay_id = #{payId}
  </select>
  <insert id="save">
    insert into pay_info (`pay_id`, `user_id`,`order_ids`,`biz_pay_no`,`sys_type`,`pay_status`,
                          `pay_amount`,`version`,`callback_content`,`callback_time`,`confirm_time`)
    values (#{payInfo.payId},#{payInfo.userId},#{payInfo.orderIds},#{payInfo.bizPayNo},#{payInfo.sysType},#{payInfo.payStatus},
            #{payInfo.payAmount},#{payInfo.version},#{payInfo.callbackContent},#{payInfo.callbackTime},#{payInfo.confirmTime});
  </insert>
  <update id="update">
    update pay_info
    <set>
      <if test="payInfo.userId != null">
        `user_id` = #{payInfo.userId},
      </if>
      <if test="payInfo.bizPayNo != null">
        `biz_pay_no` = #{payInfo.bizPayNo},
      </if>
      <if test="payInfo.sysType != null">
        `sys_type` = #{payInfo.sysType},
      </if>
      <if test="payInfo.payStatus != null">
        `pay_status` = #{payInfo.payStatus},
      </if>
      <if test="payInfo.payAmount != null">
        `pay_amount` = #{payInfo.payAmount},
      </if>
      <if test="payInfo.version != null">
        `version` = #{payInfo.version},
      </if>
      <if test="payInfo.callbackContent != null">
        `callback_content` = #{payInfo.callbackContent},
      </if>
      <if test="payInfo.callbackTime != null">
        `callback_time` = #{payInfo.callbackTime},
      </if>
      <if test="payInfo.confirmTime != null">
        `confirm_time` = #{payInfo.confirmTime},
      </if>
    </set>
    where pay_id = #{payInfo.payId}
  </update>
  <delete id="deleteById">
    delete from pay_info where pay_id = #{payId}
  </delete>

  <select id="getPayStatusByOrderIds" resultType="java.lang.Integer">
    select pay_status from pay_info where order_ids = #{orderIds}
  </select>

  <select id="isPay" resultType="java.lang.Integer">
    select count(*) from pay_info where order_ids = #{orderIds} and pay_status = 1 and user_id = #{userId}
  </select>
</mapper>
