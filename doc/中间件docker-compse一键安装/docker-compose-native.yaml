version: "3.5"

services:
  mall4cloud-mysql:
    image: mysql:8.0
    container_name: mall4cloud-mysql
    restart: always
    environment:
      - MYSQL_ROOT_PASSWORD=80jpnH4.r5g
    network_mode: "host"
    expose:
      - 3306
    volumes:
      - ./mysql/data:/var/lib/mysql
      - ./mysql/conf.d:/etc/mysql/conf.d
      - ./mysql/initdb:/docker-entrypoint-initdb.d

  mall4cloud-minio:
    image: minio/minio:RELEASE.2024-04-18T19-09-19Z
    container_name: mall4cloud-minio
    restart: always
    command: server /data --console-address ":9001"
    network_mode: "host"
    expose:
      - 9000
      - 9001
    volumes:
      - ./minio/data:/data
    environment:
      - MINIO_ROOT_USER=admin
      - MINIO_ROOT_PASSWORD=80jpnH4.r5g
      - MINIO_BROWSER_REDIRECT_URL=http://************:9001

  mall4cloud-redis:
    image: redis:7.0
    container_name: mall4cloud-redis
    restart: always
    network_mode: "host"
    command: redis-server --requirepass 80jpnH4.r5g
    expose:
      - 6379


  mall4cloud-nacos:
    image: nacos/nacos-server:v2.3.2
    container_name: mall4cloud-nacos
    restart: always
    depends_on:
      - mall4cloud-mysql
    network_mode: "host"
    expose:
      - 8848
      - 9848
      - 9849
    environment:
      - JVM_XMS=256m
      - JVM_XMX=256m
      - MODE=standalone
      - PREFER_HOST_MODE=hostname
      - SPRING_DATASOURCE_PLATFORM=mysql
      - MYSQL_SERVICE_HOST=************
      - MYSQL_SERVICE_DB_NAME=mall4cloud_nacos
      - MYSQL_SERVICE_USER=root
      - MYSQL_SERVICE_PASSWORD=80jpnH4.r5g
      - NACOS_AUTH_ENABLE=true
      - NACOS_CORE_AUTH_PLUGIN_NACOS_TOKEN_SECRET_KEY=TWFsbDRqTWFsbDRjbG91ZE1hbGw0ak1hbGw0Y2xvdWRNYWxsNGpNYWxsNGNsb3Vk
      - NACOS_CORE_AUTH_SERVER_IDENTITY_KEY=mall4jmall4jmall4jmall4jmall4jmall4jmall4jmall4j
      - NACOS_CORE_AUTH_SERVER_IDENTITY_VALUE=mall4cloudmall4cloudmall4cloudmall4cloudmall4cloudmall4cloudmall4cloud
    volumes:
      - ./nacos/logs:/home/<USER>/logs

  mall4cloud-seata:
    image: seataio/seata-server:2.0.0
    container_name: mall4cloud-seata
    restart: always
    network_mode: "host"
    expose:
      - 8091
      - 7091
    environment:
      - TZ
      - STORE_MODE=db
      - SEATA_IP=************
      - SEATA_PORT=8091
    volumes:
      - ./seata/application.yml:/seata-server/resources/application.yml

  mall4cloud-elasticsearch:
    image: elasticsearch:7.17.21
    container_name: mall4cloud-elasticsearch
    restart: always
    network_mode: "host"
    expose:
      - 9200
      - 9300
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms512m -Xmx512m
      - ELASTICSEARCH_USERNAME=elastic
      - ELASTIC_PASSWORD=80jpnH4.r5g
      - xpack.security.enabled=true
    volumes:
      - ./elasticsearch/config/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml
      - ./elasticsearch/data:/usr/share/elasticsearch/data
      - ./elasticsearch/plugins:/usr/share/elasticsearch/plugins

  mall4cloud-canal:
    image: canal/canal-server:v1.1.7
    container_name: mall4cloud-canal
    restart: always
    network_mode: "host"
    expose:
      - 11111
    volumes:
      - ./canal/conf/example:/home/<USER>/canal-server/conf/example
      - ./canal/conf/canal.properties:/home/<USER>/canal-server/conf/canal.properties
      - ./canal/logs:/home/<USER>/canal-server/logs


  mall4cloud-rocketmq-namesrv:
    image: apache/rocketmq:5.2.0
    container_name: mall4cloud-rocketmq-namesrv
    restart: always
    network_mode: "host"
    expose:
      - 9876
    volumes:
      - ./rocketmq/namesrv/logs:/home/<USER>/logs
      - ./rocketmq/namesrv/store:/home/<USER>/store
    environment:
      JAVA_OPT_EXT: "-Duser.home=/home/<USER>"
    command: ["sh","mqnamesrv"]

  mall4cloud-rocketmq-broker:
    image: apache/rocketmq:5.2.0
    container_name: mall4cloud-rocketmq-broker
    restart: always
    network_mode: "host"
    expose:
      - 10909
      - 10911
    volumes:
      - ./rocketmq/broker/logs:/home/<USER>/logs
      - ./rocketmq/broker/store:/home/<USER>/store
      - ./rocketmq/broker/conf/broker.conf:/etc/rocketmq/broker.conf
    environment:
      JAVA_OPT_EXT: "-Duser.home=/home/<USER>"
    command: ["sh","mqbroker","-c","/etc/rocketmq/broker.conf","-n","************:9876","autoCreateTopicEnable=true"]
    depends_on:
      - mall4cloud-rocketmq-namesrv


  mall4cloud-rocketmq-dashboard:
    image: fsckzy01/rocketmq-dashboard:1.0.1
    container_name: mall4cloud-rocketmq-dashboard
    restart: always
    ports:
      - 8180:8080
    environment:
      JAVA_OPTS: "-Drocketmq.namesrv.addr=************:9876 -Dcom.rocketmq.sendMessageWithVIPChannel=false"
    depends_on:
      - mall4cloud-rocketmq-namesrv
