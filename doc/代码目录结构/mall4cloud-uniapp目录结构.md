```
├── public                     # 公共文件目录
│     └── index.html           # html模版 
├── src                        # 源代码
│   ├── components             # 公共组件
│   ├── js_sdk                 # uniapp第三方插件目录
│   ├── packageActivities      # 活动功能分包
│   ├── packageShop            # 店铺功能分包
│   ├── pages                  # 主包
│   ├── static                 # 静态资源
│   │   │── empty-img          # 提示图片
│   │   │── images             # 各页面的图片资源
│   │   └── tabbar             # 底部tab栏的图标
│   ├── utils                  # 存放通用工具
│   ├── wxs                    # wxs文件目录  
│   ├── app.css                # 全局样式
│   ├── App.vue                # 入口页面
│   ├── main.js                # 初始化入口文件
│   ├── mainfest.json          # uniapp项目配置文件
│   ├── pages.json             # 全局页面配置文件
│   ├── popup.css              # 公共弹窗css样式
│   ├── router.js              # 导航路由
│   └── uni.scss               # uni-app内置的常用样式变量
├── .eslintignore              # eslint忽略配置
├── .eslintrc.js               # eslint规则制定文件
├── babel.config.js            # babel配置
├── package-lock.json          # 锁定安装时包的版本号
├── package.json               # package.json 项目基本信息
├── postcss.config             # postcss配置文件
└── vue.config.js              # vue-cli 配置
```
