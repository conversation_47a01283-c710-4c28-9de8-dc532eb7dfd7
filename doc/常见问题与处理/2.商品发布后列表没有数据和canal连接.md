# 常见问题处理

## 一、创建更新商品后，列表数据错误

- 常见问题一、成功发布商品后商品列表没有看到新发布的商品

- 常见问题二、成功修改了商品的信息，但列表中的商品数据未刷新

- 常见问题三、下单成功，但用户订单列表或商家后台中的订单列表中都没有该订单的数据


### 以下为问题排查方案

注意：中间件的配置更改完成后，数据并不会自动同步，还需要执行sql来触发同步。
```sql
商品的同步sql为：
update mall4cloud_product set update_time = now();


订单的数据同步sql为：
update mall4cloud_order set update_time = now();
```


#### 1.canal没有读取到mysql的binlog

mysql查询binglog位置

```mysql
SHOW MASTER STATUS
```

编辑`./canal/conf/example/instance.properties`

修改以下四个参数

```properties
# 填写数据库地址
canal.instance.master.address=************:3306
# 填写mysql执行命令`SHOW MASTER STATUS`后的File内容
canal.instance.master.journal.name=mysql-binlog.000001



# username/password
# 填写数据库账号
canal.instance.dbUsername=canal
# 填写数据库密码
canal.instance.dbPassword=canal
```

#### 2.canal没有连接上RocketMQ

根据 `./canal/logs/example` 中的日志文件  `example.log` 判断无法连接mq


编辑`./canal/conf/canal.properties`


```properties
# 填写RocketMQ地址
rocketmq.namesrv.addr = ************:9876
```


重启`canal`


```shell
docker restart mall4cloud-canal
```


#### 3. canal其他错误
大部分错误可以根据 `./canal/logs/example` 中  `example.log`  的报错信息查找到解决方案

但修改配置后要记得重启对应的容器，使配置生效


#### 4. search服务消费mq

canal的日志文件 `./canal/logs/example/example.log` 不再报错，但es中的商品数据还是没有更新


es商品数据保存流程：mall4cloud-product.spu表中的数据发生变动，canal监听到变动，发送mq，`mall4cloud-search`服务消费mq， 并更新es中的商品数据


排查步骤：es中的商品数据没有更新，可以先检查`mall4cloud-search`服务是否正常运行，如果正常运行，再看日志中mq的消费情况
- 如果没有mq消费的日志，可以去mq控制台看看是没有消费，还是canal没有发送mq
- canal有mq，但是服务没有消费，这种情况是mq名称不一致，如果没有更改代码，可以忽略此问题
- 还有就是mq消费失败了，这种情况大部分是es中没有创建索引或者索引结构异常导致






