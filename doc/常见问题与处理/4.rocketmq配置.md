## 一、配置

### 1. 确认rocketmq地址

![img_1.png](../img/常见问题及处理/mq-01.png)

nacos的`application-dev.yml`配置文件中，找到rocketmq的配置，并与服务日志中的rocketmq地址对比，确保一致
如果不一致，基本都是nacos缓存导致服务读取的配置并不是最新配置， 重启nacos后，再重启所有java服务


需要在seata容器启动之前更改完成，填写正确的MySQL地址、账号和密码。如果容器启动之后再更改配置文件，则重启下容器。

### 2. No route info of this topic: stock-unlock-topic

![img.png](../img/常见问题及处理/seata-05.png)

原因：挂载文件权限不足，导致容器构建时没有读取到rocketmq的配置文件，还是使用了默认配置

解决步骤： 
1. 删除rocketmq相关的容器， `docker rm -f mall4cloud-rocketmq-namesrv mall4cloud-rocketmq-broker mall4cloud-rocketmq-dashboard`
2. 进入部署的文件夹路径下，配置rocketmq文件夹权限， `chmod -R 777 rocketmq/`
3. 重新构建rockermq容器， `docker compose up -d mall4cloud-rocketmq-namesrv mall4cloud-rocketmq-broker mall4cloud-rocketmq-dashboard`
4. 重启所有java服务
