## 文件上传

本系统支持minio文件上传

文件上传的配置，一般配置一遍就不需要配置了。

文件上传的流程分成两种：

1. 将文件上传到服务器，再通过服务器上传到minio保存，再保存到本地。这种上传形式是需要消耗两倍的流量的
2. 通过服务器返回一个token之类的密钥，然后前端有直接上传到minio的权限。这种上传形式只需要消耗单次上传的流量

我们采用的是第二种上传的形式，因为要前端去兼容minio的上传，所以不仅是后台，前端也是需要做文件上传的配置的

首先我们要修改后台的文件上传配置，后台的文件上传配置在 `nacos` 的配置中心进行配置

登录 `nacos` ,进入配置管理 - 配置列表，根据生产环境or测试环境不同，选择不同的命名空间，如测试环境是`public` 的命名空间

根据打包的配置，找到`application-{环境}.yml`进行编辑

```yaml
biz:
  oss:
    # resources-url是带有bucket的
    resources-url: http://192.168.1.46:9000/mall4cloud
    type: 1
    endpoint: http://192.168.1.46:9000
    bucket: mall4cloud
    access-key-id: admin
    access-key-secret: admin123456
```

这里对这些变量进行下解释：

- type: 文件上传类型 1.minio
- bucket: 文件上传归档的一个桶(当成是一个最大的文件夹就好)
  - 对`minio`在中间件搭建的时候创建的桶，参考中间件一键安装，创建的bucket
- access-key-id: 
  - minio可以直接根据docker启动的命令获取账号密码，这里取的是`MINIO_ROOT_USER`，也就是登录的账号
- access-key-secret: 
  - minio可以直接根据docker启动的命令获取账号密码，这里取的是`MINIO_ROOT_PASSWORD`，也就是登录的密码
- endpoint: 文件上传的时候，需要上传的路径
  - minio就是minio的路径
- resources-url: resources-url是带有bucket的
  - minio就是minio的路径 + bucket

除了后台要修改图片上传的配置，前端也是需要修改文件上传配置的

1. `mall4cloud-admin`、`mall4cloud-platform`、`mall4cloud-uniapp`对于这两个项目修改根目录下的`.env.{环境}`相关文件，如开发环境修改`.env.development`文件。

- VITE_APP_RESOURCES_URL:  对应上面后台配置的resources-url
- VITE_APP_RESOURCES_TYPE： 对应上面后台配置的type

