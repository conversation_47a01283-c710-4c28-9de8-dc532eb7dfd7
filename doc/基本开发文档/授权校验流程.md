## 授权校验流程

为了确保系统的安全与获取用户信息，一般情况下都是用token解决的，那么我们的系统，token是如何生成，又是如何校验的呢？

### token的生成

在 `TokenStore` 有几个方法

```java
public class TokenStore {
	/**
	 * 将用户的部分信息存储在token中，并返回token信息
	 * @param userInfoInToken 用户在token中的信息
	 * @return token信息
	 */
    public TokenInfoBO storeAccessToken(UserInfoInTokenBO userInfoInToken) {}
    /**
	 * 根据accessToken 获取用户信息
	 * @param accessToken accessToken
	 * @param needDecrypt 是否需要解密
	 * @return 用户信息
	 */
	public ServerResponseEntity<UserInfoInTokenBO> getUserInfoByAccessToken(String accessToken, boolean needDecrypt) {}
    
	/**
	 * 刷新token，并返回新的token
	 * @param refreshToken
	 * @return
	 */
	public ServerResponseEntity<TokenInfoBO> refreshToken(String refreshToken) {}
}
```

在`LoginController#login()` 方法中，登录完毕之后使用`storeAccessToken`将登录的用户信息保存在redis中

### token的校验

在我们的设计当中，会一个授权中心，专门用于用户的授权登录，并校验token。从而不需要在每个服务都去创建自身的授权方法。

我们用商品的服务`mall4cloud-product`来举例，我们可以发现在`pom.xml`中依赖了`mall4cloud-common-security`模块。

在模块中有个过滤器`AuthFilter`，里面有这么一段

```java
tokenFeignClient.checkToken(accessToken)
```

其中`tokenFeignClient` 是 `mall4cloud-api-auth` 模块的方法，该接口其实是`feign`的一个接口，而实现就是`mall4cloud-auth`进行实现。因为我们说过，我们的认证授权应该是一个统一的服务来的，而这个服务就是`mall4cloud-auth`服务。也就是说项目启动，几乎是必须启动该项目先的。

### 配置不需要授权就能访问的url

其实并不是所有url都应该登录才能够被用户所访问到的，如浏览商品，搜索商品的时候，用户是不需要登录就能进行的操作，这个时候该怎么办呢？我们在回到我们的`AuthFilter`，里面有一段

```java
List<String> excludePathPatterns = authConfigAdapter.excludePathPatterns();
```
这里边有个`authConfigAdapter`其实实现该类就能将对应的连接设置为可以访问，或不可以访问了。

### 用户角色权限
在用户角色权限的模型中，一个用户的权限往往是需要登录才能知道的。也细化到每个url，每个方法某个用户是否能够访问。我们的系统有的需要rbac模型，有的不需要，所以我们提取了一个rbac模型的服务`mall4cloud-rbac`。我们回到`AuthFilter`，里面有一段

```java
// 省略...
authConfigAdapter.needRbac() && !checkRbac(userInfoInToken, req.getRequestURI(), req.getMethod())
// 省略...
permissionFeignClient.checkPermission(checkPermissionDTO)
// 省略...
```

这里面的`permissionFeignClient` 其实也是一个feign服务，用于连接 `mall4cloud-rbac` 这个服务，进行rbac模型的校验。
