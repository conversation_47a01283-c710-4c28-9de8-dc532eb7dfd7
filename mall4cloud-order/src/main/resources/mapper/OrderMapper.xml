<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mall4j.cloud.order.mapper.OrderMapper">
    <resultMap id="orderMap" type="com.mall4j.cloud.order.model.Order">
        <id column="order_id" property="orderId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="shop_id" property="shopId"/>
        <result column="user_id" property="userId"/>
        <result column="order_addr_id" property="orderAddrId"/>
        <result column="shop_name" property="shopName"/>
        <result column="total" property="total"/>
        <result column="status" property="status"/>
        <result column="delivery_type" property="deliveryType"/>
        <result column="close_type" property="closeType"/>
        <result column="all_count" property="allCount"/>
        <result column="pay_time" property="payTime"/>
        <result column="delivery_time" property="deliveryTime"/>
        <result column="finally_time" property="finallyTime"/>
        <result column="cancel_time" property="cancelTime"/>
        <result column="is_payed" property="isPayed"/>
        <result column="delete_status" property="deleteStatus"/>
    </resultMap>

    <resultMap type="com.mall4j.cloud.order.model.Order" id="orderAndOrderItem" extends="orderMap">
        <collection property="orderItems" columnPrefix="oi_" ofType="com.mall4j.cloud.order.model.OrderItem">
            <id column="order_item_id" jdbcType="BIGINT" property="orderItemId"/>
            <result column="shop_id" property="shopId"/>
            <result column="order_id" property="orderId"/>
            <result column="spu_id" property="spuId"/>
            <result column="sku_id" property="skuId"/>
            <result column="user_id" property="userId"/>
            <result column="count" property="count"/>
            <result column="spu_name" property="spuName"/>
            <result column="sku_name" property="skuName"/>
            <result column="pic" property="pic"/>
            <result column="delivery_type" property="deliveryType"/>
            <result column="shop_cart_time" property="shopCartTime"/>
            <result column="price" property="price"/>
            <result column="spu_total_amount" property="spuTotalAmount"/>
        </collection>
    </resultMap>

    <resultMap type="com.mall4j.cloud.api.order.bo.EsOrderBO" id="esOrderAndOrderItemAndUserAddrMap" extends="orderMap">
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="consignee" jdbcType="VARCHAR" property="consignee"/>
        <collection property="orderItems" columnPrefix="oi_" ofType="com.mall4j.cloud.order.model.OrderItem">
            <id column="order_item_id" jdbcType="BIGINT" property="orderItemId"/>
            <result column="shop_id" property="shopId"/>
            <result column="order_id" property="orderId"/>
            <result column="spu_id" property="spuId"/>
            <result column="sku_id" property="skuId"/>
            <result column="user_id" property="userId"/>
            <result column="count" property="count"/>
            <result column="spu_name" property="spuName"/>
            <result column="sku_name" property="skuName"/>
            <result column="pic" property="pic"/>
            <result column="delivery_type" property="deliveryType"/>
            <result column="shop_cart_time" property="shopCartTime"/>
            <result column="price" property="price"/>
            <result column="spu_total_amount" property="spuTotalAmount"/>
        </collection>
    </resultMap>

    <sql id="Vo_Column_List">
        `order_id`,
        `create_time`,
        `update_time`,
        `shop_id`,
        `user_id`,
        `order_addr_id`,
        `shop_name`,
        `total`,
        `status`,
        `delivery_type`,
        `close_type`,
        `all_count`,
        `pay_time`,
        `delivery_time`,
        `finally_time`,
        `cancel_time`,
        `is_payed`,
        `delete_status`
    </sql>
    <update id="update">
        update `order`
        <set>
            <if test="order.shopId != null">
                `shop_id` = #{order.shopId},
            </if>
            <if test="order.shopName != null">
                `shop_name` = #{order.shopName},
            </if>
            <if test="order.userId != null">
                `user_id` = #{order.userId},
            </if>
            <if test="order.orderAddrId != null">
                `order_addr_id` = #{order.orderAddrId},
            </if>
            <if test="order.total != null">
                `total` = #{order.total},
            </if>
            <if test="order.status != null">
                `status` = #{order.status},
            </if>
            <if test="order.deliveryType != null">
                `delivery_type` = #{order.deliveryType},
            </if>
            <if test="order.closeType != null">
                `close_type` = #{order.closeType},
            </if>
            <if test="order.allCount != null">
                `all_count` = #{order.allCount},
            </if>
            <if test="order.payTime != null">
                `pay_time` = #{order.payTime},
            </if>
            <if test="order.deliveryTime != null">
                `delivery_time` = #{order.deliveryTime},
            </if>
            <if test="order.finallyTime != null">
                `finally_time` = #{order.finallyTime},
            </if>
            <if test="order.cancelTime != null">
                `cancel_time` = #{order.cancelTime},
            </if>
            <if test="order.isPayed != null">
                `is_payed` = #{order.isPayed},
            </if>
            <if test="order.deleteStatus != null">
                `delete_status` = #{order.deleteStatus}
            </if>
        </set>
        where order_id = #{order.orderId}
    </update>
    <delete id="deleteById">
        delete from `order` where order_id = #{orderId}
    </delete>
    <insert id="saveBatch">
        insert into `order` (`order_id`, `shop_id`, `shop_name`, `user_id`, `order_addr_id`, `total`, `status`, `delivery_type`,`close_type`,
        `all_count`, `pay_time`, `delivery_time`, `finally_time`, `cancel_time`, `is_payed`, `delete_status`)
        values
        <foreach collection="orders" item="order" separator=",">
            (#{order.orderId}, #{order.shopId}, #{order.shopName}, #{order.userId}, #{order.orderAddrId}, #{order.total}, #{order.status}, #{order.deliveryType},
            #{order.closeType}, #{order.allCount}, #{order.payTime}, #{order.deliveryTime}, #{order.finallyTime},
            #{order.cancelTime}, #{order.isPayed}, #{order.deleteStatus})
        </foreach>
    </insert>
    <select id="getOrdersStatus" resultType="com.mall4j.cloud.api.order.bo.OrderStatusBO">
        select `status`, `order_id` from `order` where order_id in
        <foreach collection="orderIds" open="(" item="orderId" close=")" separator=",">
            #{orderId}
        </foreach>
    </select>
    <select id="getOrdersActualAmount" resultType="com.mall4j.cloud.api.order.vo.OrderAmountVO">
        select SUM(total) as payAmount from `order` where order_id in
        <foreach collection="orderIds" open="(" item="orderId" close=")" separator=",">
            #{orderId}
        </foreach>
    </select>
    <update id="updateByToPaySuccess">
        update `order` set `status` = 2,is_payed =1,update_time=NOW(),pay_time=NOW()
        where order_id in
        <foreach collection="orderIds" item="orderId" separator="," open="(" close=")">
            #{orderId}
        </foreach>
    </update>
    <select id="getOrdersSimpleAmountInfo" resultType="com.mall4j.cloud.api.order.bo.OrderSimpleAmountInfoBO">
        select `shop_id`, `order_id`, actual_total, platform_amount,`status`,close_type
        from `order`
        where order_id in
        <foreach collection="orderIds" open="(" item="orderId" close=")" separator=",">
            #{orderId}
        </foreach>
    </select>
    <update id="cancelOrders">
        update `order` set `status`=6,cancel_time = NOW(),update_time=NOW() where is_payed = 0 and order_id in
        <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
    </update>
    <select id="getOrderByOrderIdAndUserId" resultMap="orderMap">
        select o.* from `order` o where o.order_id = #{orderId} and o.user_id = #{userId}
    </select>
    <update id="receiptOrder">
        update `order`
        set `status` = 5,
            finally_time = NOW(),
            update_time = NOW()
        where order_id = #{orderId} and `status` = 3
    </update>
    <update id="deleteOrder">
        UPDATE `order` SET `delete_status` = 2 where order_id = #{orderId}
    </update>
    <select id="getOrderByOrderIdAndShopId" resultMap="orderMap">
        select o.* from `order` o where o.order_id = #{orderId} and o.shop_id = #{shopId}
    </select>
    <select id="getOrderAndOrderItemData" resultMap="orderAndOrderItem">
        SELECT o.user_id,
        o.*,
        oi.`order_item_id` oi_order_item_id ,
        oi.`shop_id` oi_shop_id,
        oi.`order_id` oi_order_id,
        oi.`spu_id` oi_spu_id,
        oi.`sku_id` oi_sku_id,
        oi.`user_id` oi_user_id,
        oi.`count` oi_count,
        oi.`spu_name` oi_spu_name,
        oi.`sku_name` oi_sku_name,
        oi.`pic` oi_pic,
        oi.`delivery_type` oi_delivery_type,
        oi.`shop_cart_time` oi_shop_cart_time,
        oi.`price` oi_price,
        oi.`spu_total_amount` oi_spu_total_amount
        from `order` o
        join order_item oi on o.order_id = oi.order_id
        where o.order_id = #{orderId}
        <if test="shopId != null">
            and o.shop_id = #{shopId}
        </if>
    </select>
    <select id="getSubmitOrderPayAmountInfo" resultType="com.mall4j.cloud.order.bo.SubmitOrderPayAmountInfoBO">
        select sum(total) as totalFee, max(create_time) as createTime from `order` where order_id in
        <foreach collection="orderIdList" item="orderId" separator="," close=")" open="(">
            #{orderId}
        </foreach>
    </select>
    <select id="getEsOrder" resultMap="esOrderAndOrderItemAndUserAddrMap">
        SELECT o.user_id,
               o.*,
               oi.`order_item_id`              oi_order_item_id,
               oi.`shop_id`                    oi_shop_id,
               oi.`order_id`                   oi_order_id,
               oi.`spu_id`                     oi_spu_id,
               oi.`sku_id`                     oi_sku_id,
               oi.`user_id`                    oi_user_id,
               oi.`count`                      oi_count,
               oi.`spu_name`                   oi_spu_name,
               oi.`sku_name`                   oi_sku_name,
               oi.`pic`                        oi_pic,
               oi.`delivery_type`              oi_delivery_type,
               oi.`shop_cart_time`             oi_shop_cart_time,
               oi.`price`                      oi_price,
               oi.`spu_total_amount`           oi_spu_total_amount,
               oa.consignee,
               oa.mobile
        FROM `order` AS o
        JOIN order_item oi ON o.order_id = oi.order_id
        left JOIN order_addr oa on o.order_addr_id = oa.order_addr_id
        where o.order_id = #{orderId}
    </select>

    <select id="countNumberOfStatus" resultType="com.mall4j.cloud.order.vo.OrderCountVO">
        SELECT COUNT(o.order_id)                                                  as all_count,
               COUNT(CASE WHEN o.status = 1 THEN o.order_id ELSE NULL END)        AS unPay,
               COUNT(CASE WHEN o.status = 2 THEN o.order_id ELSE NULL END)        AS payed,
               COUNT(CASE WHEN o.status = 3 THEN o.order_id ELSE NULL END)        AS consignment,
               COUNT(CASE WHEN o.status = 5 THEN o.order_id ELSE NULL END)        AS success
        FROM `order` o WHERE o.user_id = #{userId} AND o.delete_status = 0
    </select>
</mapper>
