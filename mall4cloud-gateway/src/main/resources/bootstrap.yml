server:
  port: 8000
spring:
  application:
    name: @artifactId@
  config:
    import:
      - nacos:application.${spring.cloud.nacos.config.file-extension}?refreshEnabled=true
      - nacos:${spring.application.name}.${spring.cloud.nacos.config.file-extension}?refreshEnabled=true
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_HOST:************}:${NACOS_PORT:8848}
        username: nacos
        password: nacos
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yml
        username: ${spring.cloud.nacos.discovery.username}
        password: ${spring.cloud.nacos.discovery.password}

