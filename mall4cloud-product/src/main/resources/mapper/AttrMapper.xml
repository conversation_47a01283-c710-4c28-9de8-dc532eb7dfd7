<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mall4j.cloud.product.mapper.AttrMapper">
  <resultMap id="attrMap" type="com.mall4j.cloud.product.model.Attr">
    <id column="attr_id" property="attrId" />
    <result column="create_time" property="createTime"/>
    <result column="update_time" property="updateTime"/>
    <result column="name" property="name"/>
    <result column="desc" property="desc"/>
  </resultMap>
  <resultMap id="attrListMap" type="com.mall4j.cloud.api.product.vo.AttrVO">
    <id column="attr_id" property="attrId" />
    <result column="create_time" property="createTime"/>
    <result column="update_time" property="updateTime"/>
    <result column="name" property="name"/>
    <result column="shop_id" property="shopId"/>
    <result column="desc" property="desc"/>
    <result column="search_type" property="searchType"/>
    <result column="attr_type" property="attrType"/>
    <collection property="attrValues" ofType="com.mall4j.cloud.api.product.vo.AttrValueVO">
      <id column="attr_value_id" property="attrValueId"/>
      <result column="value" property="value"/>
    </collection>
  </resultMap>
  <sql id="Vo_Column_List">
    `attr_id`,`shop_id`,`create_time`,`update_time`,`name`,`desc`,`search_type`,`attr_type`
  </sql>
  <select id="list" resultMap="attrListMap">
    select a.*,av.attr_value_id,av.value from
    (
        select <include refid="Vo_Column_List"/> from attr
        <where>
          <if test="attr.name != null">
            and name = #{attr.name}
          </if>
          <if test="attr.searchType != null">
            and search_type = #{attr.searchType}
          </if>
          <if test="attr.attrType != null">
            and attr_type = #{attr.attrType}
          </if>
          <if test="attr.shopId != null">
            and shop_id = #{attr.shopId}
          </if>
        </where>
        limit #{page.begin}, #{page.size}
    ) a
    left join attr_value av on a.attr_id = av.attr_id
    order by attr_id desc
  </select>
  <select id="countAttr" resultType="java.lang.Long">
    select count(*) from attr
    <where>
        <if test="attr.name != null">
        and name = #{attr.name}
        </if>
        <if test="attr.searchType != null">
        and search_type = #{attr.searchType}
        </if>
        <if test="attr.attrType != null">
        and attr_type = #{attr.attrType}
        </if>
        <if test="attr.shopId != null">
        and shop_id = #{attr.shopId}
        </if>
    </where>
  </select>
  <select id="getByAttrId" resultMap="attrListMap">
    SELECT a.*,av.attr_value_id,av.value FROM attr AS a
    LEFT JOIN attr_value av ON a.attr_id = av.attr_id
    WHERE a.attr_id = #{attrId}
  </select>
  <insert id="save" useGeneratedKeys="true" keyProperty="attrId">
    insert into attr (`name`,`desc`,`search_type`,`attr_type`,`shop_id`)
    values (#{attr.name},#{attr.desc},#{attr.searchType},#{attr.attrType},#{attr.shopId})
  </insert>
  <update id="update">
    update attr
    set
      <if test="attr.name != null">
        `name` = #{attr.name},
      </if>
      <if test="attr.desc != null">
        `desc` = #{attr.desc},
      </if>
      <if test="attr.searchType != null">
        `search_type` = #{attr.searchType},
      </if>
    update_time = NOW()
    where attr_id = #{attr.attrId}
  </update>
  <delete id="deleteById">
    delete from attr where attr_id = #{attrId};
    delete from attr_value where attr_id = #{attrId};
    delete from attr_category where attr_id = #{attrId};
  </delete>

  <select id="getAttrsByCategoryIdAndAttrType" resultMap="attrListMap">
    SELECT a.*,av.attr_value_id,av.value FROM attr AS a
    JOIN `attr_category` ac ON ac.attr_id = a.attr_id AND ac.category_id = #{categoryId}
    LEFT JOIN attr_value av ON a.attr_id = av.attr_id
    where a.attr_type = 1
  </select>

   <select id="getShopAttrs" resultMap="attrListMap">
    SELECT a.*,av.attr_value_id,av.value FROM attr AS a
    LEFT JOIN attr_value av ON a.attr_id = av.attr_id
    WHERE a.shop_id = #{shopId} AND a.attr_type = 0
  </select>
</mapper>
